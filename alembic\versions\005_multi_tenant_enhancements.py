"""Multi-tenant enhancements - RLS, limits, and audit

Revision ID: 005_multi_tenant_enhancements
Revises: 004_add_auth_user_uid_to_users
Create Date: 2025-08-07 08:15:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '005_multi_tenant_enhancements'
down_revision: Union[str, None] = '004_add_auth_user_uid_to_users'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create workspace_limits table
    op.create_table(
        'workspace_limits',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('max_projects', sa.Integer(), nullable=False, server_default='10'),
        sa.Column('max_documents', sa.Integer(), nullable=False, server_default='100'),
        sa.Column('max_storage_mb', sa.Integer(), nullable=False, server_default='1024'),
        sa.Column('max_team_members', sa.Integer(), nullable=False, server_default='5'),
        sa.Column('max_api_calls_per_day', sa.Integer(), nullable=False, server_default='10000'),
        sa.Column('max_ai_tokens_per_month', sa.Integer(), nullable=False, server_default='100000'),
        sa.Column('max_ai_requests_per_day', sa.Integer(), nullable=False, server_default='100'),
        sa.Column('is_trial', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('trial_end_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('current_storage_mb', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('current_api_calls_today', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('current_ai_tokens_this_month', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('current_ai_requests_today', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('plan_type', sa.String(50), nullable=False, server_default='free'),
        sa.Column('plan_features', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workspace_id')
    )

    # Create workspace_usage table
    op.create_table(
        'workspace_usage',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('usage_type', sa.String(50), nullable=False),
        sa.Column('resource_id', sa.String(100), nullable=True),
        sa.Column('quantity', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_workspace_usage_workspace_id', 'workspace_usage', ['workspace_id'])
    op.create_index('ix_workspace_usage_type', 'workspace_usage', ['usage_type'])

    # Create workspace_audit_logs table
    op.create_table(
        'workspace_audit_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(50), nullable=False),
        sa.Column('action', sa.String(100), nullable=False),
        sa.Column('resource_type', sa.String(50), nullable=False),
        sa.Column('resource_id', sa.String(100), nullable=True),
        sa.Column('old_values', sa.Text(), nullable=True),
        sa.Column('new_values', sa.Text(), nullable=True),
        sa.Column('ip_address', sa.String(45), nullable=True),
        sa.Column('user_agent', sa.String(500), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_audit_workspace_id', 'workspace_audit_logs', ['workspace_id'])
    op.create_index('ix_audit_user_id', 'workspace_audit_logs', ['user_id'])
    op.create_index('ix_audit_created_at', 'workspace_audit_logs', ['created_at'])

    # Add unique constraints for slug per workspace
    op.create_unique_constraint(
        'uq_workspace_slug', 
        'workspaces', 
        ['slug']
    )

    # Add workspace_id to missing tables
    tables_to_add_workspace = [
        'editor_generative_actions',
        'editor_generative_prompts',
        'editor_generative_variables',
        'editor_generative_prompt_variables'
    ]
    
    for table in tables_to_add_workspace:
        if table != 'editor_generative_prompt_variables':
            op.add_column(table, sa.Column('workspace_id', sa.Integer(), nullable=True))
            op.create_foreign_key(
                f'fk_{table}_workspace',
                table,
                'workspaces',
                ['workspace_id'],
                ['id'],
                ondelete='CASCADE'
            )
            op.create_index(f'ix_{table}_workspace_id', table, ['workspace_id'])

    # Enable RLS on all tables
    tables_with_rls = [
        'workspaces', 'projects', 'lots', 'tcompanies', 'stakeholders',
        'documents', 'technical_documents', 'editor_generative_actions',
        'editor_generative_prompts', 'editor_generative_variables',
        'workspace_limits', 'workspace_usage', 'workspace_audit_logs'
    ]
    
    for table in tables_with_rls:
        op.execute(f'ALTER TABLE {table} ENABLE ROW LEVEL SECURITY;')
        
        # Create RLS policies
        op.execute(f"""
            CREATE POLICY {table}_workspace_isolation ON {table}
            FOR ALL TO authenticated
            USING (
                workspace_id IN (
                    SELECT workspace_id 
                    FROM workspace_user_roles 
                    WHERE user_id = auth.uid()
                )
            );
        """)

    # Create default limits for existing workspaces
    op.execute("""
        INSERT INTO workspace_limits (workspace_id, created_at)
        SELECT id, NOW() FROM workspaces
        WHERE id NOT IN (SELECT workspace_id FROM workspace_limits);
    """)


def downgrade() -> None:
    # Drop RLS policies
    tables_with_rls = [
        'workspaces', 'projects', 'lots', 'tcompanies', 'stakeholders',
        'documents', 'technical_documents', 'editor_generative_actions',
        'editor_generative_prompts', 'editor_generative_variables',
        'workspace_limits', 'workspace_usage', 'workspace_audit_logs'
    ]
    
    for table in tables_with_rls:
        op.execute(f'DROP POLICY IF EXISTS {table}_workspace_isolation ON {table};')
        op.execute(f'ALTER TABLE {table} DISABLE ROW LEVEL SECURITY;')

    # Drop tables
    op.drop_table('workspace_audit_logs')
    op.drop_table('workspace_usage')
    op.drop_table('workspace_limits')
    
    # Drop workspace_id columns
    tables_to_remove_workspace = [
        'editor_generative_actions',
        'editor_generative_prompts',
        'editor_generative_variables',
        'editor_generative_prompt_variables'
    ]
    
    for table in tables_to_remove_workspace:
        if table != 'editor_generative_prompt_variables':
            op.drop_constraint(f'fk_{table}_workspace', table, type_='foreignkey')
            op.drop_index(f'ix_{table}_workspace_id', table)
            op.drop_column(table, 'workspace_id')