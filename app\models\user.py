# app/models/user.py
from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.sql import func
from app.db.session import Base

class User(Base):
    """
    Modèle utilisateur - table public.users liée à auth.users via auth_user_uid
    """
    __tablename__ = "users"
    
    id = Column(String(50), primary_key=True, index=True)  # ID interne
    auth_user_uid = Column(String(50), unique=True, index=True, nullable=False)  # Lien vers auth.users (sans FK)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255))
    avatar_url = Column(String(500))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
