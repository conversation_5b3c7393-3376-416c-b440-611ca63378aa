# app/models/rbac.py
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base

class Role(Base):
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text)
    is_system_role = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    # Note: These relationships are simplified due to string-based role references
    pass

class Permission(Base):
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    resource = Column(String(50), nullable=False, index=True)
    action = Column(String(50), nullable=False, index=True)
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    role_permissions = relationship("WorkspaceRolePermission", back_populates="permission")

class WorkspaceUserRole(Base):
    """
    Association entre utilisateur et workspace avec rôle
    """
    __tablename__ = "workspace_user_roles"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String(50), nullable=False, index=True)  # Supabase UUID
    role_name = Column(String(50), nullable=False, index=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    assigned_by = Column(String(50))
    
    # Relationships
    workspace = relationship("Workspace", back_populates="user_roles")
    
    # Indexes for performance
    __table_args__ = (
        UniqueConstraint('workspace_id', 'user_id', name='uq_workspace_user'),
        Index('idx_user_workspace_lookup', 'user_id', 'workspace_id'),
        Index('idx_workspace_users', 'workspace_id', 'role_name'),
    )

class WorkspaceRolePermission(Base):
    """
    Permissions par rôle et workspace
    """
    __tablename__ = "workspace_role_permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    role_name = Column(String(50), nullable=False, index=True)
    permission_id = Column(Integer, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    permission = relationship("Permission", back_populates="role_permissions")
    
    # Indexes for performance
    __table_args__ = (
        UniqueConstraint('workspace_id', 'role_name', 'permission_id', name='uq_workspace_role_permission'),
        Index('idx_workspace_role_lookup', 'workspace_id', 'role_name'),
        Index('idx_role_permissions', 'role_name', 'permission_id'),
    )
