"""Update enums to use proper enum types

Revision ID: 1a9096911fd8
Revises: 003_add_construction_enums
Create Date: 2025-08-06 22:17:29.337799

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1a9096911fd8'
down_revision: Union[str, None] = '003_add_construction_enums'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('workspace_role_permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.Column('role_name', sa.String(length=50), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id', 'role_name', 'permission_id', name='uq_workspace_role_permission')
    )
    op.create_index('idx_role_permissions', 'workspace_role_permissions', ['role_name', 'permission_id'], unique=False)
    op.create_index('idx_workspace_role_lookup', 'workspace_role_permissions', ['workspace_id', 'role_name'], unique=False)
    op.create_index(op.f('ix_workspace_role_permissions_id'), 'workspace_role_permissions', ['id'], unique=False)
    op.create_index(op.f('ix_workspace_role_permissions_permission_id'), 'workspace_role_permissions', ['permission_id'], unique=False)
    op.create_index(op.f('ix_workspace_role_permissions_role_name'), 'workspace_role_permissions', ['role_name'], unique=False)
    op.create_index(op.f('ix_workspace_role_permissions_workspace_id'), 'workspace_role_permissions', ['workspace_id'], unique=False)
    op.create_table('workspace_user_roles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(length=50), nullable=False),
    sa.Column('role_name', sa.String(length=50), nullable=False),
    sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('assigned_by', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id', 'user_id', name='uq_workspace_user')
    )
    op.create_index('idx_user_workspace_lookup', 'workspace_user_roles', ['user_id', 'workspace_id'], unique=False)
    op.create_index('idx_workspace_users', 'workspace_user_roles', ['workspace_id', 'role_name'], unique=False)
    op.create_index(op.f('ix_workspace_user_roles_id'), 'workspace_user_roles', ['id'], unique=False)
    op.create_index(op.f('ix_workspace_user_roles_role_name'), 'workspace_user_roles', ['role_name'], unique=False)
    op.create_index(op.f('ix_workspace_user_roles_user_id'), 'workspace_user_roles', ['user_id'], unique=False)
    op.create_index(op.f('ix_workspace_user_roles_workspace_id'), 'workspace_user_roles', ['workspace_id'], unique=False)
    op.drop_table('role_permissions')
    op.drop_table('workspace_members')
    op.alter_column('documents', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('documents', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('documents', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_documents_id'), 'documents', ['id'], unique=False)
    op.create_index(op.f('ix_documents_name'), 'documents', ['name'], unique=False)
    op.create_index(op.f('ix_documents_workspace_id'), 'documents', ['workspace_id'], unique=False)
    op.drop_constraint(op.f('documents_workspace_id_fkey'), 'documents', type_='foreignkey')
    op.create_foreign_key(None, 'documents', 'workspaces', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.alter_column('editor_generative_actions', 'category',
               existing_type=sa.VARCHAR(length=100),
               nullable=True,
               existing_server_default=sa.text("'general'::character varying"))
    op.alter_column('editor_generative_actions', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.drop_constraint(op.f('editor_generative_actions_workspace_id_action_key_key'), 'editor_generative_actions', type_='unique')
    op.create_index(op.f('ix_editor_generative_actions_id'), 'editor_generative_actions', ['id'], unique=False)
    op.alter_column('editor_generative_prompt_variables', 'is_required',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('editor_generative_prompt_variables', 'order_index',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('0'))
    op.drop_constraint(op.f('editor_generative_prompt_variables_prompt_id_variable_id_key'), 'editor_generative_prompt_variables', type_='unique')
    op.create_index(op.f('ix_editor_generative_prompt_variables_id'), 'editor_generative_prompt_variables', ['id'], unique=False)
    op.alter_column('editor_generative_prompts', 'model',
               existing_type=sa.VARCHAR(length=100),
               nullable=True,
               existing_server_default=sa.text("'gpt-4o'::character varying"))
    op.alter_column('editor_generative_prompts', 'max_tokens',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('2000'))
    op.alter_column('editor_generative_prompts', 'temperature',
               existing_type=sa.VARCHAR(length=10),
               nullable=True,
               existing_server_default=sa.text("'0.7'::character varying"))
    op.alter_column('editor_generative_prompts', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('editor_generative_prompts', 'is_default',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.create_index(op.f('ix_editor_generative_prompts_id'), 'editor_generative_prompts', ['id'], unique=False)
    op.alter_column('editor_generative_variables', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.drop_constraint(op.f('editor_generative_variables_workspace_id_variable_key_key'), 'editor_generative_variables', type_='unique')
    op.create_index(op.f('ix_editor_generative_variables_id'), 'editor_generative_variables', ['id'], unique=False)
    op.add_column('lots', sa.Column('order_number', sa.Integer(), nullable=True))
    op.add_column('lots', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('lots', 'phase',
               existing_type=postgresql.ENUM('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'),
               nullable=True)
    op.alter_column('lots', 'budget',
               existing_type=sa.NUMERIC(),
               type_=sa.Integer(),
               existing_nullable=True,
               postgresql_using='budget::integer')
    op.alter_column('lots', 'start_date',
               existing_type=sa.DATE(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('lots', 'end_date',
               existing_type=sa.DATE(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('lots', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('lots', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_lots_id'), 'lots', ['id'], unique=False)
    op.create_index(op.f('ix_lots_name'), 'lots', ['name'], unique=False)
    op.create_index(op.f('ix_lots_phase'), 'lots', ['phase'], unique=False)
    op.create_index(op.f('ix_lots_project_id'), 'lots', ['project_id'], unique=False)
    op.drop_constraint(op.f('lots_project_id_fkey'), 'lots', type_='foreignkey')
    op.create_foreign_key(None, 'lots', 'projects', ['project_id'], ['id'], ondelete='CASCADE')
    op.alter_column('permissions', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('permissions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_permissions_action'), 'permissions', ['action'], unique=False)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_index(op.f('ix_permissions_name'), 'permissions', ['name'], unique=True)
    op.create_index(op.f('ix_permissions_resource'), 'permissions', ['resource'], unique=False)
    op.drop_constraint(op.f('permissions_workspace_id_fkey'), 'permissions', type_='foreignkey')
    op.drop_column('permissions', 'workspace_id')
    op.add_column('projects', sa.Column('city', sa.String(length=100), nullable=True))
    op.add_column('projects', sa.Column('postal_code', sa.String(length=20), nullable=True))
    op.add_column('projects', sa.Column('country', sa.String(length=50), nullable=True))
    op.add_column('projects', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('projects', 'status',
               existing_type=postgresql.ENUM('INITIAL', 'EN_COURS', 'EN_PAUSE', 'TERMINE', 'ANNULE', name='projectstatus'),
               nullable=True)
    op.alter_column('projects', 'budget',
               existing_type=sa.NUMERIC(),
               type_=sa.Integer(),
               existing_nullable=True,
               postgresql_using='budget::integer')
    op.alter_column('projects', 'address',
               existing_type=sa.TEXT(),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('projects', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('projects', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_index(op.f('ix_projects_name'), 'projects', ['name'], unique=False)
    op.create_index(op.f('ix_projects_status'), 'projects', ['status'], unique=False)
    op.create_index(op.f('ix_projects_workspace_id'), 'projects', ['workspace_id'], unique=False)
    op.drop_constraint(op.f('projects_workspace_id_fkey'), 'projects', type_='foreignkey')
    op.create_foreign_key(None, 'projects', 'workspaces', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_column('projects', 'client_name')
    op.add_column('roles', sa.Column('is_system_role', sa.Boolean(), nullable=True))
    op.alter_column('roles', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('roles', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.drop_constraint(op.f('roles_workspace_id_fkey'), 'roles', type_='foreignkey')
    op.drop_column('roles', 'is_active')
    op.drop_column('roles', 'workspace_id')
    op.add_column('stakeholders', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('stakeholders', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('stakeholders', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_stakeholders_id'), 'stakeholders', ['id'], unique=False)
    op.create_index(op.f('ix_stakeholders_lot_id'), 'stakeholders', ['lot_id'], unique=False)
    op.create_index(op.f('ix_stakeholders_tcompany_id'), 'stakeholders', ['tcompany_id'], unique=False)
    op.drop_constraint(op.f('stakeholders_tcompany_id_fkey'), 'stakeholders', type_='foreignkey')
    op.drop_constraint(op.f('stakeholders_lot_id_fkey'), 'stakeholders', type_='foreignkey')
    op.create_foreign_key(None, 'stakeholders', 'tcompanies', ['tcompany_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'stakeholders', 'lots', ['lot_id'], ['id'], ondelete='CASCADE')
    op.add_column('tcompanies', sa.Column('code', sa.String(length=50), nullable=False))
    op.add_column('tcompanies', sa.Column('type', sa.String(length=50), nullable=True))
    op.add_column('tcompanies', sa.Column('city', sa.String(length=100), nullable=True))
    op.add_column('tcompanies', sa.Column('postal_code', sa.String(length=20), nullable=True))
    op.add_column('tcompanies', sa.Column('country', sa.String(length=50), nullable=True))
    op.add_column('tcompanies', sa.Column('description', sa.Text(), nullable=True))
    op.alter_column('tcompanies', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('tcompanies', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('tcompanies', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_tcompanies_code'), 'tcompanies', ['code'], unique=False)
    op.create_index(op.f('ix_tcompanies_id'), 'tcompanies', ['id'], unique=False)
    op.create_index(op.f('ix_tcompanies_name'), 'tcompanies', ['name'], unique=False)
    op.create_index(op.f('ix_tcompanies_workspace_id'), 'tcompanies', ['workspace_id'], unique=False)
    op.drop_constraint(op.f('tcompanies_workspace_id_fkey'), 'tcompanies', type_='foreignkey')
    op.create_foreign_key(None, 'tcompanies', 'workspaces', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.add_column('technical_documents', sa.Column('project_id', sa.Integer(), nullable=False))
    op.add_column('technical_documents', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('technical_documents', 'document_type',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('technical_documents', 'version',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=True,
               postgresql_using='version::integer')
    op.alter_column('technical_documents', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('technical_documents', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_technical_documents_id'), 'technical_documents', ['id'], unique=False)
    op.create_index(op.f('ix_technical_documents_lot_id'), 'technical_documents', ['lot_id'], unique=False)
    op.create_index(op.f('ix_technical_documents_project_id'), 'technical_documents', ['project_id'], unique=False)
    op.create_index(op.f('ix_technical_documents_title'), 'technical_documents', ['title'], unique=False)
    op.drop_constraint(op.f('technical_documents_workspace_id_fkey'), 'technical_documents', type_='foreignkey')
    op.drop_constraint(op.f('technical_documents_lot_id_fkey'), 'technical_documents', type_='foreignkey')
    op.drop_constraint(op.f('technical_documents_created_by_fkey'), 'technical_documents', type_='foreignkey')
    op.create_foreign_key(None, 'technical_documents', 'projects', ['project_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'technical_documents', 'lots', ['lot_id'], ['id'], ondelete='CASCADE')
    op.drop_column('technical_documents', 'created_by')
    op.drop_column('technical_documents', 'workspace_id')
    op.drop_column('technical_documents', 'status')
    op.add_column('users', sa.Column('full_name', sa.String(length=255), nullable=True))
    # Drop foreign key constraint before changing column type
    op.drop_constraint(op.f('workspaces_owner_id_fkey'), 'workspaces', type_='foreignkey')
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=50),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.drop_index(op.f('ix_users_supabase_uid'), table_name='users')
    op.drop_constraint(op.f('users_email_key'), 'users', type_='unique')
    op.drop_constraint(op.f('users_supabase_uid_key'), 'users', type_='unique')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.drop_column('users', 'first_name')
    op.drop_column('users', 'last_name')
    op.drop_column('users', 'supabase_uid')
    op.alter_column('workspace_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('workspace_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.create_index(op.f('ix_workspace_settings_id'), 'workspace_settings', ['id'], unique=False)
    op.drop_constraint(op.f('workspace_settings_workspace_id_fkey'), 'workspace_settings', type_='foreignkey')
    op.create_foreign_key(None, 'workspace_settings', 'workspaces', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.add_column('workspaces', sa.Column('logo_url', sa.String(length=500), nullable=True))
    op.alter_column('workspaces', 'slug',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('workspaces', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('workspaces', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('workspaces', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.drop_constraint(op.f('workspaces_slug_key'), 'workspaces', type_='unique')
    op.drop_index(op.f('ix_workspaces_slug'), table_name='workspaces')
    op.create_index(op.f('ix_workspaces_slug'), 'workspaces', ['slug'], unique=True)
    op.create_index(op.f('ix_workspaces_id'), 'workspaces', ['id'], unique=False)
    op.create_index(op.f('ix_workspaces_name'), 'workspaces', ['name'], unique=False)
    op.drop_column('workspaces', 'owner_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workspaces', sa.Column('owner_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key(op.f('workspaces_owner_id_fkey'), 'workspaces', 'users', ['owner_id'], ['id'])
    op.drop_index(op.f('ix_workspaces_name'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_id'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_slug'), table_name='workspaces')
    op.create_index(op.f('ix_workspaces_slug'), 'workspaces', ['slug'], unique=False)
    op.create_unique_constraint(op.f('workspaces_slug_key'), 'workspaces', ['slug'], postgresql_nulls_not_distinct=False)
    op.alter_column('workspaces', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('workspaces', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('workspaces', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('workspaces', 'slug',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('workspaces', 'logo_url')
    op.drop_constraint(None, 'workspace_settings', type_='foreignkey')
    op.create_foreign_key(op.f('workspace_settings_workspace_id_fkey'), 'workspace_settings', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_workspace_settings_id'), table_name='workspace_settings')
    op.alter_column('workspace_settings', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('workspace_settings', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.add_column('users', sa.Column('supabase_uid', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('last_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('first_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_unique_constraint(op.f('users_supabase_uid_key'), 'users', ['supabase_uid'], postgresql_nulls_not_distinct=False)
    op.create_unique_constraint(op.f('users_email_key'), 'users', ['email'], postgresql_nulls_not_distinct=False)
    op.create_index(op.f('ix_users_supabase_uid'), 'users', ['supabase_uid'], unique=False)
    op.alter_column('users', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('users', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('users', 'id',
               existing_type=sa.String(length=50),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    op.drop_column('users', 'full_name')
    op.add_column('technical_documents', sa.Column('status', postgresql.ENUM('BROUILLON', 'REVIEW', 'APPROVED', 'REJECTED', 'ARCHIVED', name='documentstatus'), autoincrement=False, nullable=False))
    op.add_column('technical_documents', sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('technical_documents', sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'technical_documents', type_='foreignkey')
    op.drop_constraint(None, 'technical_documents', type_='foreignkey')
    op.create_foreign_key(op.f('technical_documents_created_by_fkey'), 'technical_documents', 'users', ['created_by'], ['id'])
    op.create_foreign_key(op.f('technical_documents_lot_id_fkey'), 'technical_documents', 'lots', ['lot_id'], ['id'])
    op.create_foreign_key(op.f('technical_documents_workspace_id_fkey'), 'technical_documents', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_technical_documents_title'), table_name='technical_documents')
    op.drop_index(op.f('ix_technical_documents_project_id'), table_name='technical_documents')
    op.drop_index(op.f('ix_technical_documents_lot_id'), table_name='technical_documents')
    op.drop_index(op.f('ix_technical_documents_id'), table_name='technical_documents')
    op.alter_column('technical_documents', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('technical_documents', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('technical_documents', 'version',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.alter_column('technical_documents', 'document_type',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('technical_documents', 'is_active')
    op.drop_column('technical_documents', 'project_id')
    op.drop_constraint(None, 'tcompanies', type_='foreignkey')
    op.create_foreign_key(op.f('tcompanies_workspace_id_fkey'), 'tcompanies', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_tcompanies_workspace_id'), table_name='tcompanies')
    op.drop_index(op.f('ix_tcompanies_name'), table_name='tcompanies')
    op.drop_index(op.f('ix_tcompanies_id'), table_name='tcompanies')
    op.drop_index(op.f('ix_tcompanies_code'), table_name='tcompanies')
    op.alter_column('tcompanies', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('tcompanies', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('tcompanies', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_column('tcompanies', 'description')
    op.drop_column('tcompanies', 'country')
    op.drop_column('tcompanies', 'postal_code')
    op.drop_column('tcompanies', 'city')
    op.drop_column('tcompanies', 'type')
    op.drop_column('tcompanies', 'code')
    op.drop_constraint(None, 'stakeholders', type_='foreignkey')
    op.drop_constraint(None, 'stakeholders', type_='foreignkey')
    op.create_foreign_key(op.f('stakeholders_lot_id_fkey'), 'stakeholders', 'lots', ['lot_id'], ['id'])
    op.create_foreign_key(op.f('stakeholders_tcompany_id_fkey'), 'stakeholders', 'tcompanies', ['tcompany_id'], ['id'])
    op.drop_index(op.f('ix_stakeholders_tcompany_id'), table_name='stakeholders')
    op.drop_index(op.f('ix_stakeholders_lot_id'), table_name='stakeholders')
    op.drop_index(op.f('ix_stakeholders_id'), table_name='stakeholders')
    op.alter_column('stakeholders', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('stakeholders', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_column('stakeholders', 'is_active')
    op.add_column('roles', sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('roles', sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.create_foreign_key(op.f('roles_workspace_id_fkey'), 'roles', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.alter_column('roles', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('roles', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_column('roles', 'is_system_role')
    op.add_column('projects', sa.Column('client_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'projects', type_='foreignkey')
    op.create_foreign_key(op.f('projects_workspace_id_fkey'), 'projects', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_projects_workspace_id'), table_name='projects')
    op.drop_index(op.f('ix_projects_status'), table_name='projects')
    op.drop_index(op.f('ix_projects_name'), table_name='projects')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.alter_column('projects', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('projects', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('projects', 'address',
               existing_type=sa.String(length=500),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('projects', 'budget',
               existing_type=sa.Integer(),
               type_=sa.NUMERIC(),
               existing_nullable=True)
    op.alter_column('projects', 'status',
               existing_type=postgresql.ENUM('INITIAL', 'EN_COURS', 'EN_PAUSE', 'TERMINE', 'ANNULE', name='projectstatus'),
               nullable=False)
    op.drop_column('projects', 'is_active')
    op.drop_column('projects', 'country')
    op.drop_column('projects', 'postal_code')
    op.drop_column('projects', 'city')
    op.add_column('permissions', sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key(op.f('permissions_workspace_id_fkey'), 'permissions', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_permissions_resource'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_name'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_action'), table_name='permissions')
    op.alter_column('permissions', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('permissions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_constraint(None, 'lots', type_='foreignkey')
    op.create_foreign_key(op.f('lots_project_id_fkey'), 'lots', 'projects', ['project_id'], ['id'])
    op.drop_index(op.f('ix_lots_project_id'), table_name='lots')
    op.drop_index(op.f('ix_lots_phase'), table_name='lots')
    op.drop_index(op.f('ix_lots_name'), table_name='lots')
    op.drop_index(op.f('ix_lots_id'), table_name='lots')
    op.alter_column('lots', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('lots', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('lots', 'end_date',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATE(),
               existing_nullable=True)
    op.alter_column('lots', 'start_date',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATE(),
               existing_nullable=True)
    op.alter_column('lots', 'budget',
               existing_type=sa.Integer(),
               type_=sa.NUMERIC(),
               existing_nullable=True)
    op.alter_column('lots', 'phase',
               existing_type=postgresql.ENUM('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'),
               nullable=False)
    op.drop_column('lots', 'is_active')
    op.drop_column('lots', 'order_number')
    op.drop_index(op.f('ix_editor_generative_variables_id'), table_name='editor_generative_variables')
    op.create_unique_constraint(op.f('editor_generative_variables_workspace_id_variable_key_key'), 'editor_generative_variables', ['workspace_id', 'variable_key'], postgresql_nulls_not_distinct=False)
    op.alter_column('editor_generative_variables', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.drop_index(op.f('ix_editor_generative_prompts_id'), table_name='editor_generative_prompts')
    op.alter_column('editor_generative_prompts', 'is_default',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('editor_generative_prompts', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('editor_generative_prompts', 'temperature',
               existing_type=sa.VARCHAR(length=10),
               nullable=False,
               existing_server_default=sa.text("'0.7'::character varying"))
    op.alter_column('editor_generative_prompts', 'max_tokens',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_server_default=sa.text('2000'))
    op.alter_column('editor_generative_prompts', 'model',
               existing_type=sa.VARCHAR(length=100),
               nullable=False,
               existing_server_default=sa.text("'gpt-4o'::character varying"))
    op.drop_index(op.f('ix_editor_generative_prompt_variables_id'), table_name='editor_generative_prompt_variables')
    op.create_unique_constraint(op.f('editor_generative_prompt_variables_prompt_id_variable_id_key'), 'editor_generative_prompt_variables', ['prompt_id', 'variable_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('editor_generative_prompt_variables', 'order_index',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('editor_generative_prompt_variables', 'is_required',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.drop_index(op.f('ix_editor_generative_actions_id'), table_name='editor_generative_actions')
    op.create_unique_constraint(op.f('editor_generative_actions_workspace_id_action_key_key'), 'editor_generative_actions', ['workspace_id', 'action_key'], postgresql_nulls_not_distinct=False)
    op.alter_column('editor_generative_actions', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('editor_generative_actions', 'category',
               existing_type=sa.VARCHAR(length=100),
               nullable=False,
               existing_server_default=sa.text("'general'::character varying"))
    op.drop_constraint(None, 'documents', type_='foreignkey')
    op.create_foreign_key(op.f('documents_workspace_id_fkey'), 'documents', 'workspaces', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_documents_workspace_id'), table_name='documents')
    op.drop_index(op.f('ix_documents_name'), table_name='documents')
    op.drop_index(op.f('ix_documents_id'), table_name='documents')
    op.alter_column('documents', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('documents', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('documents', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.create_table('workspace_members',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('role', postgresql.ENUM('SUPER_ADMIN', 'ADMIN', 'USER', 'VIEWER', name='userrole'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('workspace_members_user_id_fkey')),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], name=op.f('workspace_members_workspace_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('workspace_members_pkey')),
    sa.UniqueConstraint('workspace_id', 'user_id', name=op.f('workspace_members_workspace_id_user_id_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_table('role_permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name=op.f('role_permissions_permission_id_fkey')),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name=op.f('role_permissions_role_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('role_permissions_pkey')),
    sa.UniqueConstraint('role_id', 'permission_id', name=op.f('role_permissions_role_id_permission_id_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.drop_index(op.f('ix_workspace_user_roles_workspace_id'), table_name='workspace_user_roles')
    op.drop_index(op.f('ix_workspace_user_roles_user_id'), table_name='workspace_user_roles')
    op.drop_index(op.f('ix_workspace_user_roles_role_name'), table_name='workspace_user_roles')
    op.drop_index(op.f('ix_workspace_user_roles_id'), table_name='workspace_user_roles')
    op.drop_index('idx_workspace_users', table_name='workspace_user_roles')
    op.drop_index('idx_user_workspace_lookup', table_name='workspace_user_roles')
    op.drop_table('workspace_user_roles')
    op.drop_index(op.f('ix_workspace_role_permissions_workspace_id'), table_name='workspace_role_permissions')
    op.drop_index(op.f('ix_workspace_role_permissions_role_name'), table_name='workspace_role_permissions')
    op.drop_index(op.f('ix_workspace_role_permissions_permission_id'), table_name='workspace_role_permissions')
    op.drop_index(op.f('ix_workspace_role_permissions_id'), table_name='workspace_role_permissions')
    op.drop_index('idx_workspace_role_lookup', table_name='workspace_role_permissions')
    op.drop_index('idx_role_permissions', table_name='workspace_role_permissions')
    op.drop_table('workspace_role_permissions')
    # ### end Alembic commands ###
