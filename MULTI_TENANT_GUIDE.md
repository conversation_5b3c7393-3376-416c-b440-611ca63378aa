# Guide d'Implémentation Multi-Tenant

## Vue d'Ensemble

Cette architecture multi-tenant garantit une isolation complète des données entre les workspaces avec les caractéristiques suivantes :

- **Isolation physique** : Chaque workspace a ses propres données
- **Sécurité renforcée** : RLS (Row Level Security) PostgreSQL
- **Rate limiting** : Limites par workspace
- **Audit complet** : Journalisation des actions sensibles
- **Scalabilité** : Support des plans tarifaires

## Architecture

### 1. Isolation des Données

#### **Row Level Security (RLS)**
- Activée sur toutes les tables contenant des données sensibles
- Politiques automatiques basées sur `workspace_user_roles`
- Protection contre les accès non autorisés

#### **Tenant Context**
- Middleware automatique pour injecter le `workspace_id`
- Service `TenantService` pour validation d'accès
- Filtres automatiques dans les requêtes

### 2. Modèles de Données

#### **WorkspaceLimits**
- Limites configurables par workspace
- Plans tarifaires (free, starter, pro, enterprise)
- Compteurs d'utilisation en temps réel

#### **WorkspaceUsage**
- Historique détaillé de l'utilisation
- Métriques pour la facturation
- Analyse des patterns d'usage

#### **WorkspaceAuditLog**
- Journal d'audit complet
- Traçabilité des modifications
- Conformité RGPD

### 3. Services

#### **RateLimitService**
- Limitation par type d'action
- Cache Redis pour performance
- Statistiques d'utilisation

#### **TenantService**
- Validation d'accès automatique
- Gestion du contexte tenant
- Helpers pour les requêtes

## Installation et Configuration

### 1. Migration de Base de Données

```bash
# Appliquer la migration multi-tenant
alembic upgrade head

# Vérifier l'installation
alembic current
```

### 2. Configuration des Variables d'Environnement

```bash
# .env
RLS_ENABLED=true
RATE_LIMIT_ENABLED=true
AUDIT_LOG_ENABLED=true
TENANT_CACHE_TTL=300
MAX_FILE_SIZE_MB=100
```

### 3. Activation du Middleware

Dans `app/main.py`, ajouter :

```python
from app.middleware.tenant_middleware import TenantMiddleware

# Ajouter le middleware
app.add_middleware(TenantMiddleware)
```

## Utilisation

### 1. Création d'un Workspace avec Limites

```python
from app.services.tenant_service import TenantService
from app.models.workspace_limits import WorkspaceLimits

# Créer un workspace avec plan
workspace = Workspace(name="Mon Workspace", slug="mon-workspace")
db.add(workspace)
db.commit()

# Configurer les limites
limits = WorkspaceLimits(
    workspace_id=workspace.id,
    plan_type="starter",
    max_projects=10,
    max_documents=200
)
db.add(limits)
db.commit()
```

### 2. Validation d'Accès dans les Endpoints

```python
from app.services.tenant_service import get_tenant_aware_query

@app.get("/projects")
async def get_projects(request: Request, db: Session = Depends(get_db)):
    # Query automatiquement filtrée par workspace
    query = get_tenant_aware_query(db, Project, request)
    return query.all()
```

### 3. Vérification des Limites

```python
from app.services.rate_limit_service import rate_limit_service

# Vérifier avant une action
result = await rate_limit_service.check_rate_limit(
    workspace_id=workspace_id,
    limit_type="api_calls"
)

if not result['allowed']:
    raise HTTPException(
        status_code=429,
        detail="Rate limit exceeded"
    )
```

### 4. Audit Logging

```python
from app.services.audit_service import audit_service

# Enregistrer une action
await audit_service.log_action(
    workspace_id=workspace_id,
    user_id=user_id,
    action="create",
    resource_type="project",
    resource_id=project_id,
    metadata={"name": project.name}
)
```

## Sécurité

### 1. Politiques RLS

Chaque table avec `workspace_id` a une politique RLS :

```sql
CREATE POLICY table_workspace_isolation ON table_name
    FOR ALL TO authenticated
    USING (
        workspace_id IN (
            SELECT workspace_id 
            FROM workspace_user_roles 
            WHERE user_id = auth.uid()
        )
    );
```

### 2. Validation des Permissions

- Vérification automatique via middleware
- Cache Redis pour performance
- Invalidation automatique du cache

### 3. Rate Limiting

- Limites par type d'action
- Fenêtres temporelles configurables
- Statistiques en temps réel

## Monitoring

### 1. Métriques par Workspace

```python
# Récupérer les statistiques
stats = await rate_limit_service.get_usage_stats(workspace_id)

# Exemple de sortie
{
    "api_calls": {
        "current": 150,
        "limit": 1000,
        "remaining": 850,
        "reset_time": "2024-01-15T00:00:00Z"
    }
}
```

### 2. Alertes

- Détection de dépassement de limites
- Notifications aux administrateurs
- Logs d'erreurs détaillés

## Migration depuis une Architecture Mono-Tenant

### 1. Attribution des Workspaces

```sql
-- Assigner tous les projets existants au workspace par défaut
UPDATE projects SET workspace_id = 1 WHERE workspace_id IS NULL;
UPDATE documents SET workspace_id = 1 WHERE workspace_id IS NULL;
-- Répéter pour toutes les tables
```

### 2. Configuration des Limites

```sql
-- Créer les limites pour les workspaces existants
INSERT INTO workspace_limits (workspace_id, plan_type)
SELECT id, 'enterprise' FROM workspaces;
```

## Bonnes Pratiques

### 1. Toujours utiliser le contexte tenant

```python
# ✅ Bon
query = get_tenant_aware_query(db, Model, request)

# ❌ Mauvais
query = db.query(Model)  # Pas de filtre workspace
```

### 2. Vérifier les limites avant les actions

```python
# ✅ Bon
await rate_limit_service.check_rate_limit(workspace_id, "api_calls")

# ❌ Mauvais
# Pas de vérification de limites
```

### 3. Journaliser les actions sensibles

```python
# ✅ Bon
await audit_service.log_action(...)

# ❌ Mauvais
# Pas de journalisation
```

## Support et Maintenance

### 1. Logs et Debugging

- Logs détaillés dans `/var/log/orbis/tenant.log`
- Monitoring via Prometheus/Grafana
- Alertes Slack/Email

### 2. Support Client

- Interface admin pour gérer les limites
- Dashboard de monitoring par workspace
- Outils de debug pour les équipes support

## Roadmap

- [ ] Support des sous-domaines personnalisés
- [ ] White-label complet
- [ ] API de facturation
- [ ] Support multi-région
- [ ] Backup/restauration par workspace