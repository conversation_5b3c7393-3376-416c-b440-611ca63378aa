-- Script SQL pour ajouter les contraintes après migration
-- À exécuter après la migration Alembic

-- 1. Ajouter la FK vers auth.users (optionnel, pour intégrité référentielle)
-- Note: Cette FK ne peut pas être créée via Alembic car auth.users n'est pas dans notre modèle

-- 2. Ajouter la contrainte d'unicité et l'index sur auth_user_uid
ALTER TABLE public.users 
ADD CONSTRAINT uk_users_auth_user_uid UNIQUE (auth_user_uid);

-- 3. Créer un index pour les performances
CREATE INDEX IF NOT EXISTS idx_users_auth_user_uid ON public.users(auth_user_uid);

-- 4. Vérifier la structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users'
ORDER BY ordinal_position;

-- 5. Vérifier les contraintes
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    LEFT JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.table_name='users';

-- 6. Script pour créer automatiquement les profils manquants
-- À exécuter après déploiement pour les utilisateurs existants
INSERT INTO public.users (id, auth_user_uid, email, full_name, created_at, updated_at)
SELECT 
    gen_random_uuid()::text,
    u.id,
    u.email,
    COALESCE(u.raw_user_meta_data->>'full_name', ''),
    NOW(),
    NOW()
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.users pu 
    WHERE pu.auth_user_uid = u.id
);