# app/api/v1/api.py
from fastapi import APIRouter
from app.api.v1.endpoints import (
    auth, workspaces, projects, lots, stakeholders,
    tcompanies, technical_documents, documents, editor_generative, users
)

api_router = APIRouter()

# Auth endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# User endpoints
api_router.include_router(users.router, prefix="/users", tags=["users"])

# Core endpoints
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(lots.router, prefix="/lots", tags=["lots"])
api_router.include_router(stakeholders.router, prefix="/stakeholders", tags=["stakeholders"])
api_router.include_router(tcompanies.router, prefix="/tcompanies", tags=["tcompanies"])
api_router.include_router(technical_documents.router, prefix="/technical-documents", tags=["technical-documents"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])

# Editor generative endpoints
api_router.include_router(
    editor_generative.router,
    prefix="/editor-generative",
    tags=["editor-generative"]
)
