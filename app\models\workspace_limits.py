# app/models/workspace_limits.py
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, Numeric, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base

class WorkspaceLimits(Base):
    """Limites et quotas par workspace pour le multi-tenant"""
    __tablename__ = "workspace_limits"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, unique=True)
    
    # Limites de ressources
    max_projects = Column(Integer, default=10)
    max_documents = Column(Integer, default=100)
    max_storage_mb = Column(Integer, default=1024)  # 1GB
    max_team_members = Column(Integer, default=5)
    max_api_calls_per_day = Column(Integer, default=10000)
    
    # Limites IA
    max_ai_tokens_per_month = Column(Integer, default=100000)
    max_ai_requests_per_day = Column(Integer, default=100)
    
    # Statut
    is_trial = Column(Boolean, default=True)
    trial_end_date = Column(DateTime(timezone=True))
    
    # Usage actuel
    current_storage_mb = Column(Integer, default=0)
    current_api_calls_today = Column(Integer, default=0)
    current_ai_tokens_this_month = Column(Integer, default=0)
    current_ai_requests_today = Column(Integer, default=0)
    
    # Plan tarifaire
    plan_type = Column(String(50), default="free")  # free, starter, pro, enterprise
    plan_features = Column(Text)  # JSON des features activées
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace", back_populates="limits")

class WorkspaceUsage(Base):
    """Historique d'utilisation par workspace"""
    __tablename__ = "workspace_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Type d'usage
    usage_type = Column(String(50), nullable=False)  # api_call, ai_request, storage_upload, etc.
    
    # Détails
    resource_id = Column(String(100))  # ID de la ressource concernée
    quantity = Column(Integer, default=1)  # Quantité utilisée
    metadata = Column(Text)  # JSON avec détails supplémentaires
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    workspace = relationship("Workspace")

class WorkspaceAuditLog(Base):
    """Journal d'audit pour les actions sensibles"""
    __tablename__ = "workspace_audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String(50), nullable=False, index=True)
    
    # Action
    action = Column(String(100), nullable=False)  # create, update, delete, access
    resource_type = Column(String(50), nullable=False)  # project, document, user, etc.
    resource_id = Column(String(100))
    
    # Détails
    old_values = Column(Text)  # JSON des anciennes valeurs
    new_values = Column(Text)  # JSON des nouvelles valeurs
    ip_address = Column(String(45))
    user_agent = Column(String(500))
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    workspace = relationship("Workspace")