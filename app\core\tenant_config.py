# app/core/tenant_config.py
from typing import Dict, Any
from pydantic import BaseSettings
import os

class TenantConfig:
    """Configuration multi-tenant globale"""
    
    # Plans tarifaires
    PLANS = {
        "free": {
            "max_projects": 3,
            "max_documents": 50,
            "max_storage_mb": 500,
            "max_team_members": 3,
            "max_api_calls_per_day": 1000,
            "max_ai_tokens_per_month": 10000,
            "max_ai_requests_per_day": 10,
            "features": ["basic_projects", "basic_documents", "basic_ai"]
        },
        "starter": {
            "max_projects": 10,
            "max_documents": 200,
            "max_storage_mb": 2048,
            "max_team_members": 10,
            "max_api_calls_per_day": 5000,
            "max_ai_tokens_per_month": 50000,
            "max_ai_requests_per_day": 50,
            "features": ["basic_projects", "basic_documents", "basic_ai", "advanced_export"]
        },
        "pro": {
            "max_projects": 50,
            "max_documents": 1000,
            "max_storage_mb": 10240,
            "max_team_members": 50,
            "max_api_calls_per_day": 25000,
            "max_ai_tokens_per_month": 250000,
            "max_ai_requests_per_day": 250,
            "features": ["all_features", "priority_support", "custom_branding"]
        },
        "enterprise": {
            "max_projects": -1,  # Unlimited
            "max_documents": -1,
            "max_storage_mb": -1,
            "max_team_members": -1,
            "max_api_calls_per_day": -1,
            "max_ai_tokens_per_month": -1,
            "max_ai_requests_per_day": -1,
            "features": ["all_features", "white_label", "dedicated_support", "custom_integration"]
        }
    }
    
    # Configuration RLS
    RLS_ENABLED = os.getenv("RLS_ENABLED", "true").lower() == "true"
    
    # Rate limiting
    RATE_LIMIT_ENABLED = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    
    # Audit logging
    AUDIT_LOG_ENABLED = os.getenv("AUDIT_LOG_ENABLED", "true").lower() == "true"
    
    # Cache settings
    TENANT_CACHE_TTL = int(os.getenv("TENANT_CACHE_TTL", "300"))  # 5 minutes
    
    # Storage settings
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    ALLOWED_FILE_TYPES = [
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "image/jpeg",
        "image/png",
        "image/webp"
    ]

class TenantSettings:
    """Paramètres dynamiques par tenant"""
    
    @staticmethod
    def get_plan_limits(plan_type: str) -> Dict[str, Any]:
        """Récupérer les limites pour un type de plan"""
        return TenantConfig.PLANS.get(plan_type, TenantConfig.PLANS["free"])
    
    @staticmethod
    def is_feature_enabled(plan_type: str, feature: str) -> bool:
        """Vérifier si une feature est activée pour un plan"""
        plan = TenantConfig.PLANS.get(plan_type, TenantConfig.PLANS["free"])
        return feature in plan.get("features", [])
    
    @staticmethod
    def get_storage_config() -> Dict[str, Any]:
        """Configuration de stockage"""
        return {
            "max_file_size_mb": TenantConfig.MAX_FILE_SIZE_MB,
            "allowed_types": TenantConfig.ALLOWED_FILE_TYPES,
            "base_path": os.getenv("STORAGE_BASE_PATH", "/app/storage")
        }

# Instance globale
tenant_config = TenantConfig()