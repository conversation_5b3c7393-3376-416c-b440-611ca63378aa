# app/api/v1/endpoints/users.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.db.session import get_db
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.services.user_service import get_user_service, UserService
from app.services.auth_service import auth_service
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=UserResponse)
async def create_user_profile(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Créer un profil utilisateur"""
    user_service = get_user_service(db)
    
    # Vérifier si l'utilisateur existe déjà
    existing_user = user_service.get_user_by_auth_uid(user_data.auth_user_uid)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User profile already exists"
        )
    
    user = user_service.create_user_profile(
        auth_user_uid=user_data.auth_user_uid,
        email=user_data.email,
        full_name=user_data.full_name
    )
    return user

@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Récupérer le profil de l'utilisateur connecté"""
    user_service = get_user_service(db)
    user = user_service.get_user_by_auth_uid(current_user["id"])
    
    if not user:
        # Créer automatiquement le profil s'il n'existe pas
        user = user_service.create_user_profile(
            auth_user_uid=current_user["id"],
            email=current_user["email"],
            full_name=current_user.get("user_metadata", {}).get("full_name", "")
        )
    
    return user

@router.put("/me", response_model=UserResponse)
async def update_current_user_profile(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Mettre à jour le profil de l'utilisateur connecté"""
    user_service = get_user_service(db)
    user = user_service.update_user_profile(current_user["id"], user_update)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    return user

@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Récupérer un utilisateur par ID"""
    user_service = get_user_service(db)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Lister tous les utilisateurs (admin seulement)"""
    user_service = get_user_service(db)
    users = user_service.list_users(skip=skip, limit=limit)
    return users