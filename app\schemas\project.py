from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import date, datetime
from app.models.enums import ProjectStatus, ProjectNature

class ProjectBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    code: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = None
    status: ProjectStatus = ProjectStatus.INITIAL
    nature: ProjectNature = ProjectNature.AFFAIRE
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    budget_total: Optional[float] = None
    address: Optional[str] = None
    client_name: Optional[str] = Field(None, max_length=255)
    client_contact: Optional[str] = Field(None, max_length=255)
    is_archived: bool = False
    photo_url: Optional[str] = Field(None, max_length=500)
    photo_filename: Optional[str] = Field(None, max_length=255)

class ProjectCreate(ProjectBase):
    workspace_id: int

class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    code: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = None
    status: Optional[ProjectStatus] = None
    nature: Optional[ProjectNature] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    budget_total: Optional[float] = None
    address: Optional[str] = None
    client_name: Optional[str] = Field(None, max_length=255)
    client_contact: Optional[str] = Field(None, max_length=255)
    is_archived: Optional[bool] = None
    photo_url: Optional[str] = Field(None, max_length=500)
    photo_filename: Optional[str] = Field(None, max_length=255)

class ProjectResponse(ProjectBase):
    id: int
    workspace_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
