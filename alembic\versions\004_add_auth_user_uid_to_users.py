"""Add auth_user_uid to users table

Revision ID: 004_add_auth_user_uid
Revises: 003_add_construction_enums
Create Date: 2025-08-06 23:22:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '004_add_auth_user_uid'
down_revision: Union[str, None] = '003_add_construction_enums'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Ajouter la colonne auth_user_uid
    op.add_column('users', sa.Column('auth_user_uid', sa.String(50), nullable=True))
    
    # Créer un index unique sur auth_user_uid
    op.create_index('ix_users_auth_user_uid', 'users', ['auth_user_uid'], unique=True)
    
    # Migrer les données existantes (si nécessaire)
    op.execute("UPDATE users SET auth_user_uid = id WHERE auth_user_uid IS NULL")
    
    # Rendre la colonne non-nullable après migration
    op.alter_column('users', 'auth_user_uid', nullable=False)


def downgrade() -> None:
    # Supprimer la colonne auth_user_uid
    op.drop_column('users', 'auth_user_uid')
    
    # Supprimer l'index
    op.drop_index('ix_users_auth_user_uid', table_name='users')