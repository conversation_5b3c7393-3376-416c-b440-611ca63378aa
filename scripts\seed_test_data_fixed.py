#!/usr/bin/env python3
"""
Comprehensive seed script for ORBIS Backend V2 test data
Creates:
- Super admin user (<EMAIL> / orbis123!)
- Workspace admin user (<EMAIL> / orbis123!)
- Test workspace
- Test project
- Test lot
- Complete RBAC setup
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.db.session import SessionLocal
from app.models.user import User
from app.models.workspace import Workspace, WorkspaceSettings, WorkspaceMember
from app.models.project import Project
from app.models.lot import Lot
from app.models.rbac import Permission, Role, RolePermission
from app.models.tcompany import TCompany
from app.models.enums import UserRole, ProjectStatus, ProjectNature, LotPhase, StakeholderRole
from app.core.config import settings

# Test data configuration
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "supabase_uid": "admin-orbis-fr",
        "first_name": "Super",
        "last_name": "Admin"
    },
    {
        "email": "<EMAIL>", 
        "supabase_uid": "test-orbis-fr",
        "first_name": "Test",
        "last_name": "User"
    }
]

TEST_WORKSPACE = {
    "name": "Test Workspace",
    "slug": "test-workspace",
    "description": "Test workspace for development and testing"
}

TEST_PROJECT = {
    "name": "Test Construction Project",
    "description": "A test project for demonstrating ORBIS features",
    "status": "INITIAL",
    "nature": "RESIDENTIAL",
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=365)).date(),
    "address": "123 Test Street, Test City",
    "client_name": "Test Client",
    "budget": 500000.00
}

TEST_LOT = {
    "name": "Test Lot 1 - Structural Work",
    "description": "Foundation and structural framework",
    "phase": "STRUCTURAL",
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=120)).date(),
    "budget": 150000.00
}

TEST_COMPANY = {
    "name": "Test Construction Company",
    "siret": "12345678901234",
    "address": "456 Builder Ave, Construction City",
    "phone": "+33 1 23 45 67 89",
    "email": "<EMAIL>",
    "website": "https://test-construction.fr"
}

def create_permissions(session: Session, workspace_id: int):
    """Create permissions for the workspace"""
    permissions = [
        # User management
        {"name": "users.create", "description": "Create users", "resource": "users", "action": "create"},
        {"name": "users.read", "description": "Read users", "resource": "users", "action": "read"},
        {"name": "users.update", "description": "Update users", "resource": "users", "action": "update"},
        {"name": "users.delete", "description": "Delete users", "resource": "users", "action": "delete"},
        
        # Project management
        {"name": "projects.create", "description": "Create projects", "resource": "projects", "action": "create"},
        {"name": "projects.read", "description": "Read projects", "resource": "projects", "action": "read"},
        {"name": "projects.update", "description": "Update projects", "resource": "projects", "action": "update"},
        {"name": "projects.delete", "description": "Delete projects", "resource": "projects", "action": "delete"},
        
        # Lot management
        {"name": "lots.create", "description": "Create lots", "resource": "lots", "action": "create"},
        {"name": "lots.read", "description": "Read lots", "resource": "lots", "action": "read"},
        {"name": "lots.update", "description": "Update lots", "resource": "lots", "action": "update"},
        {"name": "lots.delete", "description": "Delete lots", "resource": "lots", "action": "delete"},
        
        # Document management
        {"name": "documents.create", "description": "Create documents", "resource": "documents", "action": "create"},
        {"name": "documents.read", "description": "Read documents", "resource": "documents", "action": "read"},
        {"name": "documents.update", "description": "Update documents", "resource": "documents", "action": "update"},
        {"name": "documents.delete", "description": "Delete documents", "resource": "documents", "action": "delete"},
    ]
    
    created_permissions = []
    for perm_data in permissions:
        permission = Permission(
            name=perm_data["name"],
            resource=perm_data["resource"],
            action=perm_data["action"],
            description=perm_data["description"]
        )
        session.add(permission)
        session.flush()
        created_permissions.append(permission)
    
    return created_permissions

async def seed_database():
    """Main seeding function"""
    print("🌱 Starting database seeding...")
    
    # Create database session
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal.configure(bind=engine)
    
    with SessionLocal() as session:
        try:
            # Check if data already exists
            existing_users = session.query(User).count()
            if existing_users > 0:
                print("⚠️  Database already contains data. Skipping seeding.")
                return
            
            # Create users
            print("👥 Creating test users...")
            users = []
            for user_data in TEST_USERS:
                user = User(
                    supabase_uid=user_data['supabase_uid'],
                    email=user_data['email'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(user)
                session.flush()
                users.append(user)
                print(f"✅ Created user: {user.email}")
            
            # Create workspace
            print("🏢 Creating test workspace...")
            workspace = Workspace(
                name=TEST_WORKSPACE["name"],
                slug=TEST_WORKSPACE["slug"],
                description=TEST_WORKSPACE["description"],
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace)
            session.flush()
            print(f"✅ Created workspace: {workspace.name}")
            
            # Create workspace settings
            workspace_settings = WorkspaceSettings(
                workspace_id=workspace.id,
                default_currency="EUR",
                timezone="Europe/Paris",
                date_format="DD/MM/YYYY",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace_settings)
            
            # Create permissions
            print("🔐 Creating permissions...")
            permissions = create_permissions(session, workspace.id)
            
            # Assign users to workspace with roles
            print("👥 Assigning users to workspace...")
            # Super admin
            super_admin_role = WorkspaceUserRole(
                workspace_id=workspace.id,
                user_id=users[0].supabase_uid,
                role_name="SUPER_ADMIN",
                assigned_at=datetime.now(),
                assigned_by=users[0].supabase_uid
            )
            session.add(super_admin_role)
            
            # Workspace admin
            workspace_admin_role = WorkspaceUserRole(
                workspace_id=workspace.id,
                user_id=users[1].supabase_uid,
                role_name="WORKSPACE_ADMIN",
                assigned_at=datetime.now(),
                assigned_by=users[0].supabase_uid
            )
            session.add(workspace_admin_role)
            
            # Create test company
            print("🏗️ Creating test company...")
            company = TCompany(
                workspace_id=workspace.id,
                name=TEST_COMPANY["name"],
                siret=TEST_COMPANY["siret"],
                address=TEST_COMPANY["address"],
                phone=TEST_COMPANY["phone"],
                email=TEST_COMPANY["email"],
                website=TEST_COMPANY["website"],
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(company)
            session.flush()
            print(f"✅ Created company: {company.name}")
            
            # Create test project
            print("📋 Creating test project...")
            project = Project(
                workspace_id=workspace.id,
                name=TEST_PROJECT["name"],
                description=TEST_PROJECT["description"],
                status=TEST_PROJECT["status"],
                nature=TEST_PROJECT["nature"],
                start_date=TEST_PROJECT["start_date"],
                end_date=TEST_PROJECT["end_date"],
                address=TEST_PROJECT["address"],
                client_name=TEST_PROJECT["client_name"],
                budget=TEST_PROJECT["budget"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(project)
            session.flush()
            print(f"✅ Created project: {project.name}")
            
            # Create test lot
            print("🏗️ Creating test lot...")
            lot = Lot(
                project_id=project.id,
                name=TEST_LOT["name"],
                description=TEST_LOT["description"],
                phase=TEST_LOT["phase"],
                start_date=TEST_LOT["start_date"],
                end_date=TEST_LOT["end_date"],
                budget=TEST_LOT["budget"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(lot)
            session.flush()
            print(f"✅ Created lot: {lot.name}")
            
            # Commit all changes
            session.commit()
            
            print("\n🎉 Database seeding completed successfully!")
            print("\n📊 Summary:")
            print(f"   • Users: {len(users)} created")
            print(f"   • Workspaces: 1 created")
            print(f"   • Projects: 1 created")
            print(f"   • Lots: 1 created")
            print(f"   • Companies: 1 created")
            print(f"   • Permissions: {len(permissions)} created")
            
            print("\n🔑 Test Credentials:")
            print(f"   • Super Admin: <EMAIL> / orbis123!")
            print(f"   • Workspace Admin: <EMAIL> / orbis123!")
            print(f"   • Workspace: {TEST_WORKSPACE['slug']}")
            print(f"   • Project: {TEST_PROJECT['name']}")
            print(f"   • Lot: {TEST_LOT['name']}")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error during seeding: {str(e)}")
            raise
        finally:
            session.close()

if __name__ == "__main__":
    # Ensure we're in the right directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Run migrations first
    print("🔄 Running database migrations...")
    os.system("alembic upgrade head")
    
    # Seed the database
    asyncio.run(seed_database())