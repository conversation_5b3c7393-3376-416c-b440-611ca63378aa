# app/models/lot.py
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import ENUM
from app.db.session import Base
from app.models.enums import StakeholderRole, LotPhase

class Lot(Base):
    __tablename__ = "lots"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    phase = Column(ENUM(LotPhase, name='lotphase'), default=LotPhase.ESQ, index=True)
    order_number = Column(Integer, default=1)
    budget = Column(Integer)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="lots")
    stakeholders = relationship("Stakeholder", back_populates="lot")

class Stakeholder(Base):
    __tablename__ = "stakeholders"
    
    id = Column(Integer, primary_key=True, index=True)
    lot_id = Column(Integer, ForeignKey("lots.id", ondelete="CASCADE"), nullable=False, index=True)
    tcompany_id = Column(Integer, ForeignKey("tcompanies.id", ondelete="CASCADE"), nullable=False, index=True)
    role = Column(ENUM(StakeholderRole, name='stakeholderrole'), nullable=False)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    lot = relationship("Lot", back_populates="stakeholders")
    tcompany = relationship("TCompany", back_populates="stakeholders")
