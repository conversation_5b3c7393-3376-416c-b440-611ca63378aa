# app/services/user_service.py
from typing import Optional, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy import select
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.db.session import SessionLocal

class UserService:
    def __init__(self, db: Session):
        self.db = db

    def create_user_profile(self, auth_user_uid: str, email: str, full_name: Optional[str] = None) -> User:
        """Créer un profil utilisateur lié à auth.users"""
        user = User(
            auth_user_uid=auth_user_uid,
            email=email,
            full_name=full_name or ""
        )
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_user_by_auth_uid(self, auth_user_uid: str) -> Optional[User]:
        """Récupérer un utilisateur par son auth_user_uid"""
        return self.db.query(User).filter(User.auth_user_uid == auth_user_uid).first()

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Récupérer un utilisateur par son ID interne"""
        return self.db.query(User).filter(User.id == user_id).first()

    def update_user_profile(self, auth_user_uid: str, updates: UserUpdate) -> Optional[User]:
        """Mettre à jour le profil utilisateur"""
        user = self.get_user_by_auth_uid(auth_user_uid)
        if not user:
            return None
        
        update_data = updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_or_create_user(self, auth_user_uid: str, email: str, full_name: Optional[str] = None) -> User:
        """Récupérer ou créer un utilisateur"""
        user = self.get_user_by_auth_uid(auth_user_uid)
        if user:
            return user
        
        return self.create_user_profile(auth_user_uid, email, full_name)

    def delete_user_profile(self, auth_user_uid: str) -> bool:
        """Supprimer le profil utilisateur"""
        user = self.get_user_by_auth_uid(auth_user_uid)
        if not user:
            return False
        
        self.db.delete(user)
        self.db.commit()
        return True

    def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Lister tous les utilisateurs"""
        return self.db.query(User).offset(skip).limit(limit).all()

# Instance globale
def get_user_service(db: Session) -> UserService:
    return UserService(db)