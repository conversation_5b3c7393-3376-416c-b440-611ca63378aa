# app/middleware/tenant_middleware.py
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.services.auth_service import auth_service
import re

class TenantMiddleware(BaseHTTPMiddleware):
    """Middleware pour gérer automatiquement le contexte multi-tenant"""
    
    def __init__(self, app):
        super().__init__(app)
        # Patterns d'URLs qui nécessitent un workspace
        self.workspace_required_patterns = [
            r"^/api/v1/workspaces/(\d+)",
            r"^/api/v1/projects",
            r"^/api/v1/documents",
            r"^/api/v1/lots",
            r"^/api/v1/tcompanies",
            r"^/api/v1/editor-generative"
        ]
    
    async def dispatch(self, request: Request, call_next):
        # Extraire workspace_id de l'URL
        workspace_id = self._extract_workspace_id(request.url.path)
        
        if workspace_id:
            # Vérifier l'accès au workspace
            user = request.state.user if hasattr(request.state, 'user') else None
            if user:
                db = SessionLocal()
                try:
                    has_access = await auth_service.check_permission(
                        user["id"], 
                        workspace_id, 
                        "workspace.access", 
                        db
                    )
                    if not has_access:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="Access denied to this workspace"
                        )
                    
                    # Stocker le contexte tenant
                    request.state.workspace_id = workspace_id
                    
                finally:
                    db.close()
        
        response = await call_next(request)
        return response
    
    def _extract_workspace_id(self, path: str) -> int:
        """Extraire l'ID du workspace depuis l'URL"""
        for pattern in self.workspace_required_patterns:
            match = re.match(pattern, path)
            if match:
                if match.groups():
                    return int(match.group(1))
                else:
                    # Pour les routes sans ID dans l'URL, chercher dans les query params
                    return None
        return None

# Décorateur pour routes tenant-aware
def require_workspace_access(permission: str = None):
    """Décorateur pour vérifier l'accès au workspace"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # La logique de vérification sera gérée par le middleware
            return await func(*args, **kwargs)
        return wrapper
    return decorator