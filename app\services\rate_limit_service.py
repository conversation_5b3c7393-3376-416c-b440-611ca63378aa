# app/services/rate_limit_service.py
import redis
import json
from datetime import datetime, timedelta
from typing import Optional, Dict
from app.core.config import settings

class RateLimitService:
    """Service de rate limiting spécifique par workspace"""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.limits = {
            'api_calls': {'window': 3600, 'max': 10000},  # 10k per hour
            'ai_requests': {'window': 86400, 'max': 100},  # 100 per day
            'ai_tokens': {'window': 2592000, 'max': 100000},  # 100k per month
            'storage_upload': {'window': 86400, 'max': 100},  # 100 uploads per day
        }
    
    async def check_rate_limit(
        self, 
        workspace_id: int, 
        limit_type: str, 
        increment: int = 1
    ) -> Dict[str, any]:
        """Vérifier et incrémenter le compteur de rate limit"""
        
        if limit_type not in self.limits:
            raise ValueError(f"Unknown limit type: {limit_type}")
        
        config = self.limits[limit_type]
        key = f"rate_limit:{limit_type}:{workspace_id}"
        
        # Utiliser un pipeline Redis pour atomicité
        pipe = self.redis_client.pipeline()
        
        # Incrémenter le compteur
        pipe.incrby(key, increment)
        pipe.expire(key, config['window'])
        
        results = pipe.execute()
        current_count = results[0]
        
        # Vérifier si la limite est dépassée
        is_allowed = current_count <= config['max']
        
        return {
            'allowed': is_allowed,
            'current': current_count,
            'limit': config['max'],
            'reset_time': datetime.now() + timedelta(seconds=config['window'])
        }
    
    async def get_usage_stats(self, workspace_id: int) -> Dict[str, any]:
        """Récupérer les statistiques d'utilisation pour un workspace"""
        stats = {}
        
        for limit_type in self.limits:
            key = f"rate_limit:{limit_type}:{workspace_id}"
            current = int(self.redis_client.get(key) or 0)
            config = self.limits[limit_type]
            
            stats[limit_type] = {
                'current': current,
                'limit': config['max'],
                'remaining': max(0, config['max'] - current),
                'reset_time': datetime.now() + timedelta(seconds=config['window'])
            }
        
        return stats
    
    async def reset_limits(self, workspace_id: int, limit_type: str = None):
        """Réinitialiser les compteurs pour un workspace"""
        if limit_type:
            key = f"rate_limit:{limit_type}:{workspace_id}"
            self.redis_client.delete(key)
        else:
            # Reset all limits for workspace
            pattern = f"rate_limit:*:{workspace_id}"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
    
    async def check_workspace_limits(self, workspace_id: int, action: str, quantity: int = 1) -> Dict[str, any]:
        """Vérifier les limites globales du workspace"""
        from app.models.workspace_limits import WorkspaceLimits
        from app.db.session import SessionLocal
        
        db = SessionLocal()
        try:
            limits = db.query(WorkspaceLimits).filter(
                WorkspaceLimits.workspace_id == workspace_id
            ).first()
            
            if not limits:
                return {'allowed': True, 'reason': 'No limits configured'}
            
            checks = {
                'api_calls': {
                    'current': limits.current_api_calls_today,
                    'limit': limits.max_api_calls_per_day,
                    'field': 'current_api_calls_today'
                },
                'ai_requests': {
                    'current': limits.current_ai_requests_today,
                    'limit': limits.max_ai_requests_per_day,
                    'field': 'current_ai_requests_today'
                },
                'ai_tokens': {
                    'current': limits.current_ai_tokens_this_month,
                    'limit': limits.max_ai_tokens_per_month,
                    'field': 'current_ai_tokens_this_month'
                },
                'storage': {
                    'current': limits.current_storage_mb,
                    'limit': limits.max_storage_mb,
                    'field': 'current_storage_mb'
                }
            }
            
            if action in checks:
                check = checks[action]
                if check['current'] + quantity > check['limit']:
                    return {
                        'allowed': False,
                        'reason': f"{action} limit exceeded",
                        'current': check['current'],
                        'limit': check['limit']
                    }
            
            return {'allowed': True}
            
        finally:
            db.close()

# Instance globale
rate_limit_service = RateLimitService()