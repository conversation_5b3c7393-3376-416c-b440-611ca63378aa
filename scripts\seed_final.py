#!/usr/bin/env python3
"""
Final seed script for ORBIS Backend V2 test data
Updated to match new schema where users.id = supabase_uid
"""

import os
import sys
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.db.session import SessionLocal
from app.models.user import User
from app.models.workspace import Workspace, WorkspaceSettings
from app.models.project import Project
from app.models.lot import Lot, Stakeholder
from app.models.tcompany import TCompany
from app.models.enums import ProjectNature, LotPhase, StakeholderRole
from app.core.config import settings

# Test data
TEST_USERS = [
    {
        "id": "admin-orbis-fr",  # Now using supabase_uid as id
        "email": "<EMAIL>",
        "full_name": "Super Admin"
    },
    {
        "id": "test-orbis-fr",  # Now using supabase_uid as id
        "email": "<EMAIL>",
        "full_name": "Test User"
    }
]

TEST_WORKSPACE = {
    "name": "Test Workspace",
    "slug": "test-workspace",
    "description": "Test workspace for development"
}

TEST_PROJECT = {
    "name": "Test Construction Project",
    "code": "PROJ-001",
    "description": "A test project for demonstrating ORBIS features",
    "status": "INITIAL",  # French enum value
    "nature": "AFFAIRE",  # French enum value
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=365)).date(),
    "address": "123 Test Street, Test City",
    "client_name": "Test Client",
    "client_contact": "<EMAIL>",
    "budget_total": 500000.00
}

TEST_LOT = {
    "name": "Test Lot 1 - Structural Work",
    "description": "Foundation and structural framework",
    "phase": "EXE",  # French enum value
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=120)).date(),
    "budget": 150000.00
}

def seed_database():
    """Seed the database with test data"""
    print("🌱 Seeding database...")
    
    # Ensure Python path includes current directory
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal.configure(bind=engine)
    
    with SessionLocal() as session:
        try:
            # Check if data exists
            if session.query(User).count() > 0:
                print("⚠️  Database already seeded")
                return
            
            # Create users
            users = []
            for user_data in TEST_USERS:
                user = User(
                    id=user_data['id'],  # Using supabase_uid as id
                    email=user_data['email'],
                    full_name=user_data['full_name'],
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(user)
                session.flush()
                users.append(user)
                print(f"✅ Created user: {user.email}")
            
            # Create workspace
            workspace = Workspace(
                name=TEST_WORKSPACE["name"],
                slug=TEST_WORKSPACE["slug"],
                description=TEST_WORKSPACE["description"],
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace)
            session.flush()
            print(f"✅ Created workspace: {workspace.name}")
            
            # Create workspace settings
            workspace_settings = WorkspaceSettings(
                workspace_id=workspace.id,
                default_currency="EUR",
                timezone="Europe/Paris",
                date_format="DD/MM/YYYY",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace_settings)
            
            # Create project
            project = Project(
                workspace_id=workspace.id,
                name=TEST_PROJECT["name"],
                code=TEST_PROJECT["code"],
                description=TEST_PROJECT["description"],
                status=TEST_PROJECT["status"],
                nature=TEST_PROJECT["nature"],
                start_date=TEST_PROJECT["start_date"],
                end_date=TEST_PROJECT["end_date"],
                address=TEST_PROJECT["address"],
                client_name=TEST_PROJECT["client_name"],
                client_contact=TEST_PROJECT["client_contact"],
                budget_total=TEST_PROJECT["budget_total"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(project)
            session.flush()
            print(f"✅ Created project: {project.name}")
            
            # Create lot
            lot = Lot(
                project_id=project.id,
                name=TEST_LOT["name"],
                description=TEST_LOT["description"],
                phase=TEST_LOT["phase"],
                start_date=TEST_LOT["start_date"],
                end_date=TEST_LOT["end_date"],
                budget=TEST_LOT["budget"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(lot)
            session.flush()
            print(f"✅ Created lot: {lot.name}")
            
            # Create company
            company = TCompany(
                workspace_id=workspace.id,
                name="Test Construction Company",
                code="COMP-001",
                type="contractor",
                siret="12345678901234",
                address="456 Builder Ave, Construction City",
                city="Construction City",
                country="France",
                phone="+33 1 23 45 67 89",
                email="<EMAIL>",
                website="https://test-construction.fr",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(company)
            session.flush()
            print(f"✅ Created company: {company.name}")
            
            # Create stakeholder
            stakeholder = Stakeholder(
                lot_id=lot.id,
                tcompany_id=company.id,
                role="ARCHI",  # French enum value
                description="Architecte principal du projet",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(stakeholder)
            
            session.commit()
            
            print("\n🎉 Database seeded successfully!")
            print("\n🔑 Test Credentials:")
            print("   • Super Admin: <EMAIL>")
            print("   • Test User: <EMAIL>")
            print("   • Workspace: test-workspace")
            print("   • Project: Test Construction Project")
            print("   • Lot: Test Lot 1 - Structural Work")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error: {str(e)}")
            raise

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    seed_database()