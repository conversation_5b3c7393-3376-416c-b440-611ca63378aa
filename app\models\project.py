# app/models/project.py
from sqlalchemy import Column, Integer, String, DateTime, Text, Foreign<PERSON>ey, <PERSON>olean, Date, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import ENUM
from app.db.session import Base
from app.models.enums import ProjectStatus, ProjectNature

class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    code = Column(String(50), nullable=False, index=True)
    description = Column(Text)
    status = Column(ENUM(ProjectStatus, name='projectstatus'), default=ProjectStatus.INITIAL, index=True)
    nature = Column(ENUM(ProjectNature, name='projectnature'), default=ProjectNature.AFFAIRE)
    start_date = Column(Date)
    end_date = Column(Date)
    budget_total = Column(Numeric(15, 2))
    address = Column(Text)
    client_name = Column(String(255))
    client_contact = Column(String(255))
    is_archived = Column(Boolean, default=False)
    photo_url = Column(String(500))
    photo_filename = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace", back_populates="projects")
    lots = relationship("Lot", back_populates="project")
