#!/usr/bin/env python3
"""
Test d'authentification pour ORBIS Backend V2
Teste la <NAME_EMAIL> / orbis123!
"""

import pytest
import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api/v1"
AUTH_ENDPOINT = f"{BASE_URL}{API_PREFIX}/auth"

class AuthTester:
    """Classe pour tester l'authentification"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token = None
        self.refresh_token = None
        
    def test_login(self, email: str, password: str) -> Dict[str, Any]:
        """Test de connexion avec email/mot de passe"""
        print(f"\n🔐 Test de connexion avec {email}")
        
        url = f"{AUTH_ENDPOINT}/login"
        payload = {
            "email": email,
            "password": password
        }
        
        try:
            response = self.session.post(url, json=payload)
            print(f"📡 POST {url}")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.refresh_token = data.get("refresh_token")
                
                print(f"✅ Connexion réussie!")
                print(f"🎫 Access Token: {self.access_token[:50]}...")
                print(f"🔄 Refresh Token: {self.refresh_token[:50]}...")
                print(f"⏰ Expires in: {data.get('expires_in')}s")
                
                return {
                    "success": True,
                    "data": data,
                    "tokens": {
                        "access": self.access_token,
                        "refresh": self.refresh_token
                    }
                }
            else:
                print(f"❌ Erreur de connexion: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return {
                    "success": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            print(f"❌ Exception lors de la connexion: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_get_current_user(self) -> Dict[str, Any]:
        """Test de récupération des informations utilisateur"""
        if not self.access_token:
            return {"success": False, "error": "Pas de token disponible"}
            
        print(f"\n👤 Test récupération infos utilisateur")
        
        url = f"{AUTH_ENDPOINT}/me"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        try:
            response = self.session.get(url, headers=headers)
            print(f"📡 GET {url}")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                user_data = response.json()
                print(f"✅ Infos utilisateur récupérées:")
                print(f"   ID: {user_data.get('id')}")
                print(f"   Email: {user_data.get('email')}")
                print(f"   Nom: {user_data.get('full_name')}")
                
                return {
                    "success": True,
                    "data": user_data
                }
            else:
                print(f"❌ Erreur: {response.status_code}")
                return {
                    "success": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_refresh_token(self) -> Dict[str, Any]:
        """Test de rafraîchissement du token"""
        if not self.refresh_token:
            return {"success": False, "error": "Pas de refresh token disponible"}
            
        print(f"\n🔄 Test rafraîchissement token")
        
        url = f"{AUTH_ENDPOINT}/refresh"
        payload = {
            "refresh_token": self.refresh_token
        }
        
        try:
            response = self.session.post(url, json=payload)
            print(f"📡 POST {url}")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.refresh_token = data.get("refresh_token")
                
                print(f"✅ Token rafraîchi!")
                print(f"🎫 Nouveau Access Token: {self.access_token[:50]}...")
                
                return {
                    "success": True,
                    "data": data
                }
            else:
                print(f"❌ Erreur: {response.status_code}")
                return {
                    "success": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_get_workspaces(self) -> Dict[str, Any]:
        """Test de récupération des workspaces"""
        if not self.access_token:
            return {"success": False, "error": "Pas de token disponible"}
            
        print(f"\n🏢 Test récupération workspaces")
        
        url = f"{AUTH_ENDPOINT}/workspaces"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        try:
            response = self.session.get(url, headers=headers)
            print(f"📡 GET {url}")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                workspaces = response.json()
                print(f"✅ Workspaces récupérés: {len(workspaces)}")
                
                for idx, ws in enumerate(workspaces):
                    workspace = ws.get("workspace", {})
                    role = ws.get("role", "N/A")
                    print(f"   {idx+1}. {workspace.get('name')} (Rôle: {role})")
                
                return {
                    "success": True,
                    "data": workspaces
                }
            else:
                print(f"❌ Erreur: {response.status_code}")
                return {
                    "success": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_logout(self) -> Dict[str, Any]:
        """Test de déconnexion"""
        if not self.access_token:
            return {"success": False, "error": "Pas de token disponible"}
            
        print(f"\n🚪 Test déconnexion")
        
        url = f"{AUTH_ENDPOINT}/logout"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        try:
            response = self.session.post(url, headers=headers)
            print(f"📡 POST {url}")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ Déconnexion réussie")
                self.access_token = None
                self.refresh_token = None
                return {
                    "success": True,
                    "data": response.json()
                }
            else:
                print(f"❌ Erreur: {response.status_code}")
                return {
                    "success": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": str(e)
            }

def run_auth_tests():
    """Fonction principale pour exécuter tous les tests"""
    print("🚀 Lancement des tests d'authentification ORBIS")
    print("=" * 50)
    
    tester = AuthTester()
    
    # Test de connexion avec les credentials fournis
    login_result = tester.test_login("<EMAIL>", "orbis123!")
    
    if login_result["success"]:
        # Sauvegarder les tokens dans un fichier
        tokens = login_result["tokens"]
        with open("test_tokens.json", "w") as f:
            json.dump(tokens, f, indent=2)
        print(f"\n💾 Tokens sauvegardés dans test_tokens.json")
        
        # Tests supplémentaires
        tester.test_get_current_user()
        tester.test_get_workspaces()
        
        # Test de rafraîchissement
        tester.test_refresh_token()
        
        # Test de déconnexion
        tester.test_logout()
        
    else:
        print("\n❌ Impossible de continuer sans connexion réussie")
    
    print("\n" + "=" * 50)
    print("✅ Tests terminés")

if __name__ == "__main__":
    run_auth_tests()