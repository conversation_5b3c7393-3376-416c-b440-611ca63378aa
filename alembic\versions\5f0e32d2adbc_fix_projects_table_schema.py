"""fix_projects_table_schema

Revision ID: 5f0e32d2adbc
Revises: 1a9096911fd8
Create Date: 2025-08-06 23:45:01.102978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5f0e32d2adbc'
down_revision: Union[str, None] = '1a9096911fd8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add missing columns to projects table
    op.add_column('projects', sa.Column('code', sa.String(length=50), nullable=False, server_default='PROJ-001'))
    op.add_column('projects', sa.Column('client_name', sa.String(length=255), nullable=True))
    op.add_column('projects', sa.Column('client_contact', sa.String(length=255), nullable=True))
    op.add_column('projects', sa.Column('is_archived', sa.<PERSON>(), nullable=True, server_default='false'))
    op.add_column('projects', sa.Column('photo_url', sa.String(length=500), nullable=True))
    op.add_column('projects', sa.Column('photo_filename', sa.String(length=255), nullable=True))
    
    # Change budget type from Integer to Numeric
    op.alter_column('projects', 'budget',
               existing_type=sa.Integer(),
               type_=sa.Numeric(15, 2),
               existing_nullable=True,
               new_column_name='budget_total')
    
    # Update address type
    op.alter_column('projects', 'address',
               existing_type=sa.String(length=500),
               type_=sa.Text(),
               existing_nullable=True)
    
    # Create unique constraint for code
    op.create_unique_constraint('uq_projects_code_workspace', 'projects', ['code', 'workspace_id'])
    
    # Create indexes
    op.create_index('ix_projects_code', 'projects', ['code'])
    op.create_index('ix_projects_client_name', 'projects', ['client_name'])
    op.create_index('ix_projects_is_archived', 'projects', ['is_archived'])


def downgrade() -> None:
    # Remove indexes
    op.drop_index('ix_projects_is_archived', table_name='projects')
    op.drop_index('ix_projects_client_name', table_name='projects')
    op.drop_index('ix_projects_code', table_name='projects')
    op.drop_constraint('uq_projects_code_workspace', 'projects', type_='unique')
    
    # Revert budget_total back to budget and change type
    op.alter_column('projects', 'budget_total',
               existing_type=sa.Numeric(15, 2),
               type_=sa.Integer(),
               existing_nullable=True,
               new_column_name='budget')
    
    # Remove added columns
    op.drop_column('projects', 'photo_filename')
    op.drop_column('projects', 'photo_url')
    op.drop_column('projects', 'is_archived')
    op.drop_column('projects', 'client_contact')
    op.drop_column('projects', 'client_name')
    op.drop_column('projects', 'code')
    
    # Revert address type
    op.alter_column('projects', 'address',
               existing_type=sa.Text(),
               type_=sa.String(length=500),
               existing_nullable=True)
