# app/services/tenant_service.py
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import Request, HTTPException, status
from app.models.workspace import Workspace
from app.models.rbac import WorkspaceUserRole

class TenantService:
    """Service pour gérer le contexte multi-tenant"""
    
    @staticmethod
    def get_current_workspace(request: Request) -> Optional[int]:
        """Récupérer l'ID du workspace courant depuis la requête"""
        return getattr(request.state, 'workspace_id', None)
    
    @staticmethod
    def validate_workspace_access(
        db: Session, 
        user_id: str, 
        workspace_id: int, 
        permission: str = None
    ) -> bool:
        """Valider l'accès d'un utilisateur à un workspace"""
        
        # Vérifier si l'utilisateur a accès au workspace
        user_role = db.query(WorkspaceUserRole).filter(
            WorkspaceUserRole.user_id == user_id,
            WorkspaceUserRole.workspace_id == workspace_id
        ).first()
        
        if not user_role:
            return False
        
        # Si une permission spécifique est demandée
        if permission:
            from app.models.rbac import WorkspaceRolePermission, Permission
            has_permission = db.query(WorkspaceRolePermission).join(
                Permission,
                WorkspaceRolePermission.permission_id == Permission.id
            ).filter(
                WorkspaceRolePermission.workspace_id == workspace_id,
                WorkspaceRolePermission.role_name == user_role.role_name,
                Permission.name == permission
            ).first()
            
            return has_permission is not None
        
        return True
    
    @staticmethod
    def get_user_workspaces(db: Session, user_id: str) -> list:
        """Récupérer tous les workspaces accessibles à un utilisateur"""
        return db.query(Workspace).join(
            WorkspaceUserRole,
            Workspace.id == WorkspaceUserRole.workspace_id
        ).filter(
            WorkspaceUserRole.user_id == user_id,
            Workspace.is_active == True
        ).all()
    
    @staticmethod
    def add_workspace_filter(query, model, workspace_id: int):
        """Ajouter automatiquement le filtre workspace_id aux requêtes"""
        if hasattr(model, 'workspace_id'):
            return query.filter(model.workspace_id == workspace_id)
        return query

# Fonction utilitaire pour les endpoints
def get_tenant_aware_query(db: Session, model, request: Request):
    """Créer une query automatiquement filtrée par workspace"""
    workspace_id = TenantService.get_current_workspace(request)
    if not workspace_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Workspace context required"
        )
    
    query = db.query(model)
    return TenantService.add_workspace_filter(query, model, workspace_id)